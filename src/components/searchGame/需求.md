<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-29 17:06:01
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-08-01 20:35:02
 * @FilePath     : /src/components/searchGame/需求.md
 * @Description  :
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-29 17:06:01
-->

# 游戏搜索页面需求文档

## 🎯 页面布局需求

### **整体结构**

游戏搜索页面采用固定导航条 + 可滚动内容区域的布局模式，确保用户在浏览大量游戏数据时有良好的导航体验。

### **布局结构详解**

#### **1. 导航条（SearchHeader）**

- **固定在顶部**：使用 `position: sticky` 或 `position: fixed`
- **始终可见**：不随页面滚动而移动
- **置顶层级**：`z-index` 足够高，确保在最上层
- **功能**：包含返回按钮和页面标题

#### **2. 下方内容区域**

- **可滚动区域**：包含搜索栏、分类、筛选器、游戏列表
- **垂直滚动**：当游戏数据增多时，整个内容区域可以垂直滚动
- **填充剩余空间**：占据除导航条外的所有可用高度

### **滚动行为**

- **导航条**：始终固定在顶部，不参与滚动
- **内容区域**：作为一个整体进行滚动，包括：
  - 搜索栏（SearchBar）
  - 游戏分类（GameCategories）
  - 筛选器（GameFilters）
  - 游戏列表（GameGrid）

### **布局示意图**

```
┌─────────────────────────────────┐
│     SearchHeader (固定顶部)      │  ← sticky/fixed
├─────────────────────────────────┤
│                                 │
│     可滚动内容区域                │  ← 可滚动容器
│  ┌─────────────────────────────┐ │
│  │      SearchBar              │ │
│  ├─────────────────────────────┤ │
│  │    GameCategories           │ │
│  ├─────────────────────────────┤ │
│  │     GameFilters             │ │
│  ├─────────────────────────────┤ │
│  │                             │ │
│  │      GameGrid               │ │  ← 数据增多时
│  │    (游戏列表可能很长)         │ │     这里滚动
│  │                             │ │
│  └─────────────────────────────┘ │
└─────────────────────────────────┘
```

### **技术实现要点**

#### **1. 容器高度控制**

- 整个页面容器需要固定高度（如 `100vh`）
- 确保页面不会超出视口范围

#### **2. 内容区域布局**

- 除去导航条高度后的剩余空间作为可滚动区域
- 使用 `flex` 布局：`flex-direction: column`
- 导航条：`flex-shrink: 0`（不收缩）
- 内容区域：`flex: 1`（占据剩余空间）

#### **3. 滚动优化**

- 确保滚动性能良好
- 如果数据量很大，考虑虚拟滚动
- 添加适当的滚动条样式

#### **4. 响应式考虑**

- 适配不同屏幕尺寸
- 确保在移动端有良好的滚动体验
- 考虑安全区域（safe-area）

## 🏗️ 组件架构

### **组件分层**

```
SearchGameInput (触发器)
└── van-popup (右侧弹出)
    └── SearchGameIndex (主容器)
        ├── SearchHeader (固定导航条)
        └── ScrollableContent (可滚动内容)
            ├── SearchBar (搜索栏)
            ├── GameCategories (分类标签)
            ├── GameFilters (筛选器)
            └── GameGrid (游戏列表)
```

### **关键 CSS 结构**

```scss
.search-game-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;

    .search-header {
        position: sticky;
        top: 0;
        z-index: 100;
        flex-shrink: 0;
    }

    .scrollable-content {
        flex: 1;
        overflow-y: auto;
        display: flex;
        flex-direction: column;
    }
}
```

## 📱 用户体验

### **交互流程**

1. 用户点击搜索输入框
2. 从右侧弹出全屏搜索页面
3. 导航条固定在顶部，提供返回功能
4. 用户可以滚动浏览所有内容
5. 游戏列表数据增多时，整体内容区域可滚动
6. 导航条始终可见，方便随时返回

### **性能考虑**

- 大数据量时的滚动性能
- 图片懒加载
- 搜索防抖
- 组件按需加载
