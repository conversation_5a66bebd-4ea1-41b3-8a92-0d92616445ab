<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-29 15:59:30
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-08-01 21:01:42
 * @FilePath     : /src/components/searchGame/index.vue
 * @Description  : 游戏搜索页面内容组件
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-29 15:59:30
-->

<template>
    <div class="search-game-container">
        <!-- 1. 导航条 -->
        <SearchHeader @close="handleClose" />

        <!-- 2. 搜索栏 -->
        <SearchBar v-model="searchKeyword" @search="performSearch" />

        <!-- 3. 分类标签 -->
        <GameCategories :selected-category="selectedCategory" @category-select="selectCategory" />

        <!-- 4. 排序筛选器 -->
        <GameFilters
            v-model:sort-by="sortBy"
            v-model:provider="provider"
            :filtered-games="filteredGames"
            :games-before-provider-filter="gamesBeforeProviderFilter"
        />

        <!-- 5. 游戏列表 -->
        <GameGrid :games="filteredGames" @game-select="selectGame" />
    </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useBaseStore } from '@/stores'
import { Categorys } from '@/api/home/<USER>'
import SearchHeader from './SearchHeader.vue'
import SearchBar from './SearchBar.vue'
import GameCategories from './GameCategories.vue'
import GameFilters from './GameFilters.vue'
import GameGrid from './GameGrid.vue'

// 定义事件
interface Emits {
    (e: 'close'): void
}

const emit = defineEmits<Emits>()

const store = useBaseStore()

// 搜索相关状态
const searchKeyword = ref('')

// 分类和筛选状态
const selectedCategory = ref('all')
const sortBy = ref('Default')
const provider = ref('All')

// 获取所有游戏数据（从 store 中获取真实数据）
const allGames = computed(() => {
    if (!store.gameList || !store.menuData) return []

    // 在这里也可以打印 gameList
    console.log('%c------ searchGame 中的 store.gameList ------', 'background-color:teal;font-size:12px;color:#fff', store.gameList)

    // 获取所有分类的游戏
    const games = []
    store.menuData.forEach((category) => {
        const categoryGames = store.getGameList(category.type) || []
        console.log('%c------ categoryGames ', 'background-color:blue;font-size:12px;color:#fff', categoryGames)
        // 为每个游戏添加分类信息
        categoryGames.forEach((game) => {
            games.push({
                ...game,
                categoryType: category.type, // 使用 type 作为分类标识
                categoryName: category.name,
            })
        })
    })

    return games
})

// 游戏数据现在从 allGames computed 属性获取真实数据

// 计算属性：未按厂商筛选的游戏列表（用于供应商统计）
const gamesBeforeProviderFilter = computed(() => {
    // ✅ 创建数组副本，避免修改原数组
    let filtered = [...allGames.value]

    // 按分类过滤（使用 categoryType 字段，与 GameCategories 的 type 匹配）
    if (selectedCategory.value !== 'all') {
        filtered = filtered.filter((game) => game.categoryType === selectedCategory.value)
    }

    // 按搜索关键词过滤
    if (searchKeyword.value) {
        const keyword = searchKeyword.value.toLowerCase()
        filtered = filtered.filter(
            (game) =>
                (game.name && game.name.toLowerCase().includes(keyword)) ||
                (game.gamefirm && store.getGameName(game.gamefirm).toLowerCase().includes(keyword)) ||
                (game.gametag && game.gametag.toLowerCase().includes(keyword))
        )
    }

    // 注意：这里不包含按供应商的筛选，这样供应商统计就不会受到当前选中供应商的影响
    return filtered
})

// 计算属性：完全过滤后的游戏列表
const filteredGames = computed(() => {
    let filtered = [...gamesBeforeProviderFilter.value]

    // 按供应商过滤（使用 gamefirm 字段）
    if (provider.value !== 'All') {
        filtered = filtered.filter((game) => {
            const gameName = store.getGameName(game.gamefirm)
            return gameName.toLowerCase().includes(provider.value.toLowerCase())
        })
    }

    // 按名称排序 - 使用 slice() 创建副本再排序，避免修改原数组
    if (sortBy.value === 'A-Z') {
        filtered = filtered.slice().sort((a, b) => {
            const nameA = (a.name || '').toLowerCase()
            const nameB = (b.name || '').toLowerCase()
            return nameA.localeCompare(nameB)
        })
    } else if (sortBy.value === 'Z-A') {
        filtered = filtered.slice().sort((a, b) => {
            const nameA = (a.name || '').toLowerCase()
            const nameB = (b.name || '').toLowerCase()
            return nameB.localeCompare(nameA)
        })
    }
    console.log('%c------ filtered ', 'background-color:blue;font-size:12px;color:#fff', filtered)

    return filtered
})

// 方法
const handleClose = () => {
    emit('close')
}

const performSearch = () => {
    console.log('执行搜索:', searchKeyword.value)
}

const selectCategory = (category: Categorys) => {
    selectedCategory.value = category.type || 'all'

    // 切换分类时重置供应商选择为 'All'
    // 因为不同分类下的供应商列表会发生变化
    provider.value = 'All'

    console.log('选中的分类对象:', category)
    console.log('供应商选择已重置为 All')
    // 现在可以访问完整的分类信息：
    // category.type - 分类类型
    // category.name - 分类名称
    // category.picture - 分类图片
    // category.menupicture - 菜单图片
}

const selectGame = (game: any) => {
    console.log('选择游戏:', game)
    // 这里可以添加游戏选择逻辑
}
</script>

<style lang="scss" scoped>
$text-primary: #ffffff;

.search-game-container {
    background: #202222;
    height: 100vh;
    display: flex;
    flex-direction: column;
    color: $text-primary;
    overflow: auto;
}
</style>
