/*
 * <AUTHOR> <PERSON>
 * @Date         : 2025-08-01 19:22:39
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-08-01 19:44:12
 * @FilePath     : /src/components/searchGame/providerStats.ts
 * @Description  :
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-08-01 19:22:39
 */
/**
 * 供应商游戏统计工具
 * @description 基于已筛选的游戏列表处理供应商游戏数量统计
 * <AUTHOR> Assistant
 * @date 2025-08-01
 */

import { Gamefirm } from '@/api/home/<USER>'

// 游戏接口定义（基于 index.vue 中的游戏数据结构）
export interface Game {
    id: string | number
    name: string
    gamefirm: string // 对应供应商的 abbr 字段
    gametag: string
    categoryType?: string
    // 其他游戏字段...
}

// 扩展的供应商接口，包含游戏数量
export interface ProviderWithStats extends Gamefirm {
    gameCount: number
}

/**
 * 计算单个供应商在已筛选游戏列表中的数量
 * @param providerAbbr 供应商缩写（abbr 字段）
 * @param filteredGameList 已筛选的游戏列表（来自 index.vue 的 filteredGames）
 * @returns 游戏数量
 */
export function getProviderGameCount(providerAbbr: string, filteredGameList: Game[]): number {
    if (!providerAbbr || !filteredGameList || !Array.isArray(filteredGameList)) {
        return 0
    }

    return filteredGameList.filter(game =>
        game.gamefirm && game.gamefirm === providerAbbr
    ).length
}

/**
 * 为供应商列表添加基于已筛选游戏的数量统计
 * @param providers 供应商列表
 * @param filteredGameList 已筛选的游戏列表（来自 index.vue 的 filteredGames）
 * @returns 包含实时游戏数量的供应商列表
 */
export function addGameCountToProviders(
    providers: Gamefirm[],
    filteredGameList: Game[]
): ProviderWithStats[] {
    if (!providers || !Array.isArray(providers)) {
        return []
    }

    if (!filteredGameList || !Array.isArray(filteredGameList)) {
        return providers.map(provider => ({
            ...provider,
            gameCount: 0
        }))
    }

    return providers.map(provider => {
        const gameCount = getProviderGameCount(provider.abbr, filteredGameList)

        return {
            ...provider,
            gameCount
        }
    })
}

/**
 * 获取供应商统计摘要（基于已筛选的游戏列表）
 * @param providers 供应商列表
 * @param filteredGameList 已筛选的游戏列表
 * @returns 统计摘要
 */
export function getProviderStatsSummary(providers: Gamefirm[], filteredGameList: Game[]) {
    const providersWithStats = addGameCountToProviders(providers, filteredGameList)

    return {
        totalProviders: providers.length,
        totalFilteredGames: filteredGameList.length,
        providersWithGames: providersWithStats.filter(p => p.gameCount > 0).length,
        providersWithoutGames: providersWithStats.filter(p => p.gameCount === 0).length,
        averageGamesPerProvider: providers.length > 0
            ? Math.round(filteredGameList.length / providers.length * 100) / 100
            : 0
    }
}
