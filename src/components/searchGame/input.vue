<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-29 15:56:33
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-08-05 10:45:57
 * @FilePath     : /src/components/searchGame/input.vue
 * @Description  : 游戏搜索输入框组件
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-29 15:56:33
-->

<template>
    <div class="search-game-input">
        <van-search
            v-model="searchValue"
            :placeholder="$t('Search_games')"
            :show-action="false"
            shape="round"
            background="transparent"
            @search="handleSearch"
            @input="handleInput"
            @focus="handleFocus"
            @blur="handleBlur"
        />

        <!-- 搜索弹窗 -->
        <van-popup v-model:show="isSearchVisible" position="right" :style="{ width: '100%', height: '100%' }" :close-on-click-overlay="false">
            <SearchGameIndex @close="closeSearchPopup" />
        </van-popup>
    </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import SearchGameIndex from './index.vue'

// 定义组件属性
interface Props {
    modelValue?: string
    placeholder?: string
}

// 定义事件
interface Emits {
    (e: 'update:modelValue', value: string): void
    (e: 'search', value: string): void
    (e: 'input', value: string): void
    (e: 'focus', event: Event): void
    (e: 'blur', event: Event): void
}

const props = withDefaults(defineProps<Props>(), {
    modelValue: '',
    placeholder: 'Search_games',
})

const emit = defineEmits<Emits>()

// 搜索相关状态
const searchValue = ref(props.modelValue)
const isSearchVisible = ref(false)

// 监听搜索值变化，同步到父组件
watch(
    () => props.modelValue,
    (newValue) => {
        searchValue.value = newValue
    }
)

watch(searchValue, (newValue) => {
    emit('update:modelValue', newValue)
})

// 方法
const closeSearchPopup = () => {
    isSearchVisible.value = false
}

// 处理搜索事件
const handleSearch = (value: string) => {
    emit('search', value)
}

// 处理输入事件
const handleInput = (value: string) => {
    emit('input', value)
}

// 处理聚焦事件
const handleFocus = (event: Event) => {
    emit('focus', event)
    // 显示搜索弹窗
    isSearchVisible.value = true
}

// 处理失焦事件
const handleBlur = (event: Event) => {
    emit('blur', event)
}
</script>

<style lang="scss" scoped>
.search-game-input {
    width: 100%;
    padding: 0 15px;

    :deep(.van-search) {
        padding: 0;
        background: transparent;

        .van-search__content {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 8px;
            padding-left: 12px;

            .van-field {
                background: transparent;

                .van-field__left-icon {
                    color: rgba(255, 255, 255, 0.6);
                    margin-right: 8px;
                }

                .van-field__control {
                    color: #ffffff;
                    font-size: 24px; // 修改字体大小

                    &::placeholder {
                        color: rgba(255, 255, 255, 0.6);
                        font-size: 24px; // 同时修改占位符字体大小
                    }
                }
            }
        }

        // 移除默认的边框
        .van-search__content::after {
            display: none;
        }
    }
}
</style>
