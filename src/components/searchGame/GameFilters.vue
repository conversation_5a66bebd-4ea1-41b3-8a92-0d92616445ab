<!--
 * <AUTHOR> <PERSON>
 * @Date         : 2025-07-29 16:53:00
 * @LastEditors  : <PERSON>
 * @LastEditTime : 2025-08-01 20:01:04
 * @FilePath     : /src/components/searchGame/GameFilters.vue
 * @Description  : 游戏筛选器组件
 * Copyright 2025 Bruce, All Rights Reserved.
 * 2025-07-29 16:53:00
-->

<template>
    <div class="filters">
        <div class="filter-button" @click="handleSortClick">
            <span class="filter-label">Sort by:</span>
            <span class="filter-value">{{ sortBy }}</span>
            <van-icon name="arrow-down" class="filter-arrow" />
        </div>
        <div class="filter-button" @click="handleProviderClick">
            <span class="filter-label">Providers:</span>
            <span class="filter-value">{{ provider }}</span>
            <van-icon name="arrow-down" class="filter-arrow" />
        </div>
    </div>

    <!-- 排序选择弹窗 -->
    <SelectSheet
        v-model:show="showSortSheet"
        title="Select"
        :options="sortOptions"
        :selected-value="sortBy"
        custom-class="sort-action-sheet"
        @select="onSortSelect"
    />

    <!-- 供应商选择弹窗 -->
    <ProviderSheet
        v-model:show="showProviderSheet"
        title="Select"
        :providers="providerList"
        :selected-providers="selectedProviders"
        custom-class="provider-action-sheet"
        @update:selectedProviders="onProvidersUpdate"
        @clear-all="onProvidersClearAll"
    />
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import { useBaseStore } from '@/stores'
import { Gamefirm } from '@/api/home/<USER>'
import { addGameCountToProviders, type Game } from './gameSearchUtils'
import SelectSheet from './SelectSheet.vue'
import ProviderSheet from './ProviderSheet.vue'

// 定义属性
interface Props {
    sortBy?: string
    provider?: string
    filteredGames?: Game[] // 完全筛选后的游戏列表（用于显示）
    gamesBeforeProviderFilter?: Game[] // 供应商筛选前的游戏列表（用于统计）
}

// 定义事件
interface Emits {
    (e: 'update:sortBy', value: string): void
    (e: 'update:provider', value: string): void
}

const props = withDefaults(defineProps<Props>(), {
    sortBy: 'Default',
    provider: 'All',
    filteredGames: () => [], // 默认空数组
    gamesBeforeProviderFilter: () => [], // 默认空数组
})

const emit = defineEmits<Emits>()

const store = useBaseStore()

const sortBy = ref(props.sortBy)
const provider = ref(props.provider)

// 弹窗显示状态
const showSortSheet = ref(false)
const showProviderSheet = ref(false)

// 排序选项数据 - 默认顺序 + 字母排序
const sortOptions = [
    { name: 'Default', value: 'Default' },
    { name: 'A-Z', value: 'A-Z' },
    { name: 'Z-A', value: 'Z-A' },
]

// 供应商数据 - 从 store 中动态获取并添加游戏数量统计
const providerList = computed(() => {
    const providers = store.getGameFirms() as Gamefirm[]
    // 使用供应商筛选前的游戏列表进行统计，这样供应商数量不会受到当前选中供应商的影响
    const gamesForStats = props.gamesBeforeProviderFilter || []

    // 使用统计函数添加游戏数量
    const providersWithStats = addGameCountToProviders(providers, gamesForStats)

    // 过滤掉游戏数量为 0 的供应商
    return providersWithStats.filter((provider) => provider.gameCount > 0)
})

// 选中的供应商对象数组
const selectedProviders = ref<Gamefirm[]>([])

const handleSortClick = () => {
    showSortSheet.value = true
}

const handleProviderClick = () => {
    showProviderSheet.value = true
}

// 排序选择处理
const onSortSelect = (option: any) => {
    sortBy.value = option.name
    showSortSheet.value = false
    emit('update:sortBy', option.value)
}

// 供应商多选更新处理
const onProvidersUpdate = (newSelectedProviders: Gamefirm[]) => {
    selectedProviders.value = newSelectedProviders

    // 更新显示文本 - 按照设计图显示格式
    if (newSelectedProviders.length === 0) {
        provider.value = 'All'
        emit('update:provider', 'All')
    } else {
        provider.value = `+${newSelectedProviders.length}` // 显示 "+7" 格式
        // 向父组件传递provider名称，用于条件搜索
        const providerNames = newSelectedProviders.map((p) => p.name).join(',')
        emit('update:provider', providerNames)
    }
}

// 清空所有供应商选择
const onProvidersClearAll = () => {
    selectedProviders.value = []
    provider.value = 'All'
    emit('update:provider', 'All')
}

// 监听外部值变化
watch(
    () => props.sortBy,
    (newValue) => {
        sortBy.value = newValue
    }
)

// 监听外部 provider 变化，处理重置逻辑
watch(
    () => props.provider,
    (newValue) => {
        // 如果外部重置为 'All'，清空内部选中的供应商
        if (newValue === 'All') {
            selectedProviders.value = []
            provider.value = 'All'
            console.log('供应商选择已重置：外部触发')
            return
        }

        // 如果当前显示值是我们的格式化值（+数字），则不要被外部值覆盖
        const isFormattedValue = provider.value.startsWith('+') && /^\+\d+$/.test(provider.value)

        if (!isFormattedValue && newValue !== provider.value) {
            provider.value = newValue
        }
    }
)

// 监听内部值变化，同步到外部
watch(sortBy, (newValue) => {
    emit('update:sortBy', newValue)
})

// 移除 provider 的自动同步 watch，改为手动控制
</script>

<style lang="scss" scoped>
$text-primary: #ffffff;
$background-color: rgba(255, 255, 255, 0.1);

.filters {
    display: flex;
    gap: 12px;
    padding: 0 16px 24px;
    flex-shrink: 0;

    .filter-button {
        flex: 1;
        display: flex;
        align-items: center;
        background: #2c3031;
        border-radius: 6px;
        padding: 12px 16px;
        height: 80px;
        cursor: pointer;
        transition: all 0.3s ease;
        position: relative;
        font-size: 24px;

        &:hover {
            opacity: 0.8;
        }

        .filter-label {
            color: #bec7ca;
            margin-right: 4px;
            white-space: nowrap;
        }

        .filter-value {
            color: $text-primary;
            font-weight: 500;
            flex: 1;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .filter-arrow {
            width: 44px;
            height: 44px;
            color: #fff;
            flex-shrink: 0;
            margin-left: 8px;
            background-color: #404647;
            border-radius: 8px;
            padding: 2px;
            // 关键：让伪元素居中
            display: flex;
            align-items: center;
            justify-content: center;
            line-height: 1;
            // 确保伪元素的定位基准
            position: relative;
        }
    }
}
</style>
